#!/usr/bin/env python3
"""
Script to read and analyze assignment files
"""

import pdfplumber
from docx import Document
import os

def read_pdf(file_path):
    """Read PDF file and extract text"""
    print(f"\n{'='*60}")
    print(f"READING PDF: {file_path}")
    print(f"{'='*60}")
    
    try:
        with pdfplumber.open(file_path) as pdf:
            full_text = ""
            for page_num, page in enumerate(pdf.pages, 1):
                text = page.extract_text()
                if text:
                    print(f"\n--- PAGE {page_num} ---")
                    print(text)
                    full_text += f"\n--- PAGE {page_num} ---\n{text}\n"
            return full_text
    except Exception as e:
        print(f"Error reading PDF {file_path}: {e}")
        return ""

def read_docx(file_path):
    """Read DOCX file and extract text"""
    print(f"\n{'='*60}")
    print(f"READING DOCX: {file_path}")
    print(f"{'='*60}")
    
    try:
        doc = Document(file_path)
        full_text = ""
        for para in doc.paragraphs:
            if para.text.strip():
                print(para.text)
                full_text += para.text + "\n"
        return full_text
    except Exception as e:
        print(f"Error reading DOCX {file_path}: {e}")
        return ""

def main():
    # List of files to read
    files = [
        "assignment 24-25 Sem 2 (1).pdf",  # Main assignment
        "Assignment 1,516 By Prathmesh More.pdf",  # Reference 1
        "Biomaterail Assignment 2 - Copy.docx",  # Reference 2
        "biomaterial task 3 by prathmesh more.docx"  # Reference 3
    ]
    
    all_content = {}
    
    for file_path in files:
        if os.path.exists(file_path):
            if file_path.endswith('.pdf'):
                content = read_pdf(file_path)
            elif file_path.endswith('.docx'):
                content = read_docx(file_path)
            else:
                print(f"Unsupported file type: {file_path}")
                continue
            
            all_content[file_path] = content
        else:
            print(f"File not found: {file_path}")
    
    # Save all content to a text file for analysis
    with open("all_files_content.txt", "w", encoding="utf-8") as f:
        for file_path, content in all_content.items():
            f.write(f"\n{'='*80}\n")
            f.write(f"FILE: {file_path}\n")
            f.write(f"{'='*80}\n")
            f.write(content)
            f.write(f"\n{'='*80}\n\n")
    
    print(f"\n\nAll content saved to 'all_files_content.txt'")
    return all_content

if __name__ == "__main__":
    main()
