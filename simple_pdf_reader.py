#!/usr/bin/env python3

try:
    import pdfplumber
    print("pdfplumber imported successfully")
except ImportError as e:
    print(f"Import error: {e}")
    exit(1)

import os

# Check if file exists
assignment_file = "assignment 24-25 Sem 2 (1).pdf"
if os.path.exists(assignment_file):
    print(f"File exists: {assignment_file}")
    
    try:
        with pdfplumber.open(assignment_file) as pdf:
            print(f"PDF opened successfully. Number of pages: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"\n{'='*50}")
                print(f"PAGE {page_num}")
                print(f"{'='*50}")
                
                text = page.extract_text()
                if text:
                    print(text)
                else:
                    print("No text found on this page")
                    
    except Exception as e:
        print(f"Error reading PDF: {e}")
else:
    print(f"File not found: {assignment_file}")
    print("Available files:")
    for f in os.listdir("."):
        print(f"  {f}")
